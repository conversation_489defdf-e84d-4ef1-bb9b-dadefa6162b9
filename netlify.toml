# Global settings for all builds
[build]
  # This is the build command for the Next.js app.
  # It will be run from the 'client' directory.
  command = "npm run build"
  # The directory to publish, relative to the repository root.
  publish = "client/.next"
  # The directory where the client application is located.
  base = "client"

# The Essential Next.js plugin is required for Next.js sites on Netlify.
# It automatically configures your site to handle server-side rendering,
# API routes, and other Next.js features.
[[plugins]]
  package = "@netlify/plugin-nextjs"
