// /vistagram/scripts/generate-seed.js

import { GoogleGenerativeAI } from '@google/generative-ai';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables from .env file
dotenv.config();

// Configure the Google AI client with the API key
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);

// The directory where the final seed.json will be saved
const outputDir = path.resolve('../server/prisma');
const outputPath = path.join(outputDir, 'seed.json');

// The detailed prompt for the AI (this prompt does not need to change!)
const prompt = `
  Generate a JSON object containing a single key "posts". This key should hold an array of 25 realistic user posts for a travel photo-sharing app called "Vistagram". Each object in the "posts" array must have the following structure:
  {
    "username": "A creative and realistic username (e.g., wanderlust_dave, aisha_explores, globetrotter_gabe).",
    "imageSearchQuery": "A descriptive search query for a high-quality, vertical (portrait-orientation) photo of a famous Point of Interest (POI). Be specific, e.g., 'A tourist taking a photo of the Colosseum in Rome at sunrise, vertical shot'.",
    "caption": "A personal, engaging caption for the photo, 1-3 sentences long. Include 2-3 relevant hashtags like #travel, #wanderlust, etc.",
    "timestamp": "A realistic ISO 8601 timestamp from within the last three years (e.g., '2023-04-12T15:30:00Z')."
  }
  Ensure a wide diversity in global locations (continents, cities, landmarks), usernames, and caption styles. The output MUST be a valid JSON object. Do not include any text or markdown formatting before or after the JSON object itself.
`;

async function generateSeedData() {
  console.log('🤖 Contacting Google Gemini to generate Vistagram seed data...');
  try {
    // We use the 'gemini-pro' model, which is excellent for text tasks.
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash-latest' });
    
    // Make the API call
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // The rest of the logic is the same!
    const jsonData = JSON.parse(text);

    const finalData = jsonData.posts.map((post, index) => ({
      ...post,
      imageUrl: `/seed-images/${index + 1}.jpg`,
      likes: Math.floor(Math.random() * 500) + 10,
      shares: Math.floor(Math.random() * 50) + 1,
    }));

    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(outputPath, JSON.stringify(finalData, null, 2));

    console.log(`✅ Success! Seed data generated using Google Gemini at: ${outputPath}`);
    console.log('----------------------------------------------------');
    console.log('🔴 NEXT STEP: (If not already done) Download images based on "imageSearchQuery".');
    console.log('   Place them in `/vistagram/client/public/seed-images/` and name them 1.jpg, 2.jpg, etc.');
    console.log('----------------------------------------------------');

  } catch (error) {
    console.error('❌ Error generating seed data:', error);
    console.error('---');
    console.error('💡 Tip: If you get a parsing error, the AI might not have returned perfect JSON. Try running the script again.');
  }
}

generateSeedData();