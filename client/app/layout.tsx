import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Link from 'next/link';

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Vistagram",
  description: "A modern social media application",
};

// Force dynamic rendering for all pages to avoid SSR issues with NextAuth
export const dynamic = 'force-dynamic';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} bg-zinc-50 text-zinc-900`}>
        <header className="bg-white/80 backdrop-blur-sm shadow-sm sticky top-0 z-50">
          <nav className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              {/* Logo/Title */}
              <Link href="/" className="text-2xl font-bold text-gray-900 tracking-tight">
                Vistagram
              </Link>

              <div className="flex items-center space-x-4">
                {/* Auth Button */}
                <div className="text-sm text-gray-600">Auth</div>

                {/* Upload Icon Link */}
                <Link href="/upload" className="p-2 rounded-full text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors">
                  <span>📷</span>
                  <span className="sr-only">Create New Post</span>
                </Link>
              </div>
            </div>
          </nav>
        </header>
        <main className="bg-gray-50 min-h-screen">
          {children}
        </main>
      </body>
    </html>
  );
}
