import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Vistagram",
  description: "A modern social media application",
};

// Force dynamic rendering for all pages to avoid SSR issues with NextAuth
export const dynamic = 'force-dynamic';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <header>
          <nav>
            <div>
              <h1>Vistagram</h1>
            </div>
          </nav>
        </header>
        <main>
          {children}
        </main>
      </body>
    </html>
  );
}
