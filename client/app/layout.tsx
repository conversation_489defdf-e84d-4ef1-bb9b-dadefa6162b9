import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Link from 'next/link';
import { Camera } from 'lucide-react';
import NextAuthSessionProvider from "../components/SessionProvider";
import AuthButton from "../components/AuthButton";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Vistagram",
  description: "A modern social media application",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} bg-zinc-50 text-zinc-900`}>
        <NextAuthSessionProvider>
          <header className="bg-white/80 backdrop-blur-sm shadow-sm sticky top-0 z-50">
            <nav className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center h-16">
                {/* Logo/Title */}
                <Link href="/" className="text-2xl font-bold text-gray-900 tracking-tight">
                  Vistagram
                </Link>

                <div className="flex items-center space-x-4">
                  {/* Auth Button */}
                  <AuthButton />

                  {/* Upload Icon Link */}
                  <Link href="/upload" className="p-2 rounded-full text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors">
                    <Camera className="h-6 w-6" />
                    <span className="sr-only">Create New Post</span>
                  </Link>
                </div>
              </div>
            </nav>
          </header>
          <main className="bg-gray-50 min-h-screen">
            {children}
          </main>
        </NextAuthSessionProvider>
      </body>
    </html>
  );
}
