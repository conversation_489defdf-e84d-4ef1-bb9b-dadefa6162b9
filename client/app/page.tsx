"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import Link from "next/link";
import PostCard from "../components/PostCard";
import { Post } from "../src/types";

export default function Home() {
  const { data: session, status } = useSession();
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/posts`);
        if (!response.ok) {
          throw new Error('Failed to fetch posts');
        }
        const data = await response.json();
        // Transform the API response to match the expected Post type
        const transformedPosts = data.map((post: any) => ({
          ...post,
          id: post.id.toString(),
          author: {
            username: post.author?.name || 'Unknown User'
          },
          updatedAt: post.updatedAt || post.createdAt
        }));
        setPosts(transformedPosts);
      } catch (err) {
        setError('Failed to load posts');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (status === "loading" || loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  if (!session) {
    return (
      <main className="bg-gray-50 min-h-screen py-8">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Welcome to Vistagram</h1>
          <p className="text-xl text-gray-600 mb-8">Share your moments with the world</p>
          <div className="space-x-4">
            <Link href="/register" className="bg-blue-500 text-white px-8 py-3 rounded-lg hover:bg-blue-600 text-lg">
              Get Started
            </Link>
            <Link href="/login" className="border border-blue-500 text-blue-500 px-8 py-3 rounded-lg hover:bg-blue-50 text-lg">
              Sign In
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-gray-50 min-h-screen py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Welcome to Vistagram</h1>
          <p className="text-gray-700 mb-8">Welcome back, {session?.user?.name}!</p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="max-w-2xl mx-auto">
          {posts.length > 0 ? (
            posts.map((post) => (
              <PostCard key={post.id} post={post} />
            ))
          ) : (
            <div className="text-center text-gray-600">
              <p className="mb-4">No posts yet!</p>
              <Link href="/upload" className="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">
                Share Your First Post
              </Link>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
