@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

.break-inside-avoid {
  break-inside: avoid;
}

input::placeholder {
  color: #6b7280; /* gray-500 */
  opacity: 1;
}

input {
  color: #111827; /* gray-900 */
}

/* Ensure good text contrast */
.post-card {
  background: white;
  color: #111827; /* gray-900 */
}

.post-card .user-name {
  color: #111827; /* gray-900 */
}

.post-card .timestamp {
  color: #6b7280; /* gray-500 */
}

.post-card .caption {
  color: #374151; /* gray-700 */
}
