"use client";

import { useSession } from "next-auth/react";
import Link from "next/link";

export default function HomePage() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-blue-600">Vistagram</h1>
          <div className="flex gap-4">
            {session ? (
              <>
                <span className="text-gray-700">Welcome, {session.user?.name || session.user?.email}</span>
                <Link href="/upload" className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                  Upload
                </Link>
                <Link href="/api/auth/signout" className="text-gray-600 hover:text-gray-800">
                  Sign Out
                </Link>
              </>
            ) : (
              <>
                <Link href="/login" className="text-blue-600 hover:text-blue-800">
                  Sign In
                </Link>
                <Link href="/register" className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                  Sign Up
                </Link>
              </>
            )}
          </div>
        </div>
      </nav>

      <main className="max-w-4xl mx-auto px-4 py-8">
        {session ? (
          <div>
            <h2 className="text-3xl font-bold mb-8 text-center">Your Feed</h2>
            <div className="text-center text-gray-600">
              <p className="mb-4">No posts yet!</p>
              <Link href="/upload" className="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">
                Share Your First Post
              </Link>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <h2 className="text-4xl font-bold mb-4">Welcome to Vistagram</h2>
            <p className="text-xl text-gray-600 mb-8">Share your moments with the world</p>
            <div className="space-x-4">
              <Link href="/register" className="bg-blue-500 text-white px-8 py-3 rounded-lg hover:bg-blue-600 text-lg">
                Get Started
              </Link>
              <Link href="/login" className="border border-blue-500 text-blue-500 px-8 py-3 rounded-lg hover:bg-blue-50 text-lg">
                Sign In
              </Link>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
