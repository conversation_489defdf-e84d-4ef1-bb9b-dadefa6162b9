// client/src/pages/api/auth/[...nextauth].ts

import NextAuth, { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

export const authOptions: NextAuthOptions = {
  // Configure one or more authentication providers
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: 'Credentials',
      // The credentials is used to generate a suitable form on the sign in page.
      credentials: {
        email: { label: "Email", type: "text", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        if (!credentials) {
          return null;
        }

        // This is where we call our NestJS backend's login endpoint
        const res = await fetch('http://localhost:3001/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: credentials.email,
            password: credentials.password,
          }),
        });

        const data = await res.json();

        // If login is successful, the backend returns the user and a JWT.
        if (res.ok && data.access_token) {
          // Return user object with access token attached
          return {
            id: data.user.id,
            email: data.user.email,
            name: data.user.name,
            accessToken: data.access_token,
          };
        }

        // Return null if user data could not be retrieved
        return null;
      }
    })
  ],
  // We need to define session and jwt callbacks to handle the JWT flow
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    // This callback is called whenever a JWT is created or updated.
    async jwt({ token, user }) {
      // If `user` object exists (on sign in), we're adding our custom data to the token
      if (user) {
        token.accessToken = user.accessToken;
      }
      return token;
    },
    // This callback is called whenever a session is checked.
    async session({ session, token }) {
      // We're adding our custom data from the token to the session object
      (session as any).accessToken = token.accessToken;
      return session;
    },
  },
  // You can add a secret here for signing the NextAuth JWT
  // This is separate from our NestJS JWT_SECRET
  secret: process.env.NEXTAUTH_SECRET,
};

export default NextAuth(authOptions);
