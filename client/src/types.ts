// client/src/types.ts

// We will expand this later, but this defines the basic shape of a Post
export interface Post {
  id: string;
  caption: string;
  imageUrl: string;
  author: {
    username: string;
  };
  createdAt: string; // We'll use string for now, can be Date later
  likes?: number; // Optional, as not all posts may have likes initially
  shares?: number; // Optional, for the same reason as likes
}

// Sample posts array with the new fields
const posts: Post[] = [
  {
    id: "1",
    author: {
      username: "traveler123",
    },
    imageUrl: "https://example.com/image1.jpg",
    caption: "Exploring the Grand Canyon! #adventure",
    createdAt: new Date("2025-07-01T10:00:00Z").toISOString(),
    likes: 120,
    shares: 15,
  },
  // More posts...
];
