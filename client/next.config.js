/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'vistagram.blob.core.windows.net',
        port: '',
        pathname: '/posts/**',
      },
    ],
  },

  // Better error reporting during build
  typescript: {
    ignoreBuildErrors: false,
  },

  // Disable static optimization temporarily to debug
  output: process.env.NODE_ENV === 'production' ? undefined : 'standalone',

  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig;
