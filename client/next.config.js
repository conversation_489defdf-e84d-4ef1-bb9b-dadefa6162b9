/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'vistagram.blob.core.windows.net',
        port: '',
        pathname: '/posts/**',
      },
    ],
  },

  // Disable static optimization to prevent SSR issues with NextAuth
  experimental: {
    forceSwcTransforms: true,
  },

  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig;
