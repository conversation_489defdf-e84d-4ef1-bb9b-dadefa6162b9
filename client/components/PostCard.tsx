import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Post } from '../src/types';

interface PostCardProps {
  post: Post;
}

export default function PostCard({ post }: PostCardProps) {
  const { data: session } = useSession();
  const [likes, setLikes] = useState(post.likes || 0);
  const [shares, setShares] = useState(post.shares || 0);
  const [isLiking, setIsLiking] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  const handleLike = async () => {
    if (!session || isLiking) return;

    setIsLiking(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/posts/${post.id}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${(session as any).accessToken}`,
        },
      });

      if (response.ok) {
        setLikes(prev => prev + 1);
      }
    } catch (error) {
      console.error('Failed to like post:', error);
    } finally {
      setIsLiking(false);
    }
  };

  const handleShare = async () => {
    if (!session || isSharing) return;

    setIsSharing(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/posts/${post.id}/share`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${(session as any).accessToken}`,
        },
      });

      if (response.ok) {
        setShares(prev => prev + 1);
      }
    } catch (error) {
      console.error('Failed to share post:', error);
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <div className="post-card bg-white rounded-lg shadow-md mb-6 overflow-hidden">
      <img
        src={post.imageUrl}
        alt={post.caption}
        className="w-full h-64 object-cover"
      />
      <div className="p-4">
        <div className="flex items-center mb-2">
          <span className="user-name font-semibold">{post.author.username}</span>
          <span className="timestamp text-sm ml-2">{new Date(post.createdAt).toLocaleDateString()}</span>
        </div>
        <p className="caption mb-3">{post.caption}</p>

        {/* Interactive Likes and Shares Section */}
        <div className="flex items-center gap-4 text-sm">
          <button
            onClick={handleLike}
            disabled={!session || isLiking}
            className="flex items-center gap-1 text-gray-600 hover:text-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span>❤️</span>
            <span>{likes} likes</span>
          </button>
          <button
            onClick={handleShare}
            disabled={!session || isSharing}
            className="flex items-center gap-1 text-gray-600 hover:text-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span>🔄</span>
            <span>{shares} shares</span>
          </button>
        </div>
      </div>
    </div>
  );
}
