// client/src/components/Header.tsx
import Link from 'next/link';
import { useSession, signIn, signOut } from 'next-auth/react';

const Header = () => {
  const { data: session, status } = useSession();
  const loading = status === 'loading';

  return (
    <header className="bg-white shadow-md">
      <nav className="container mx-auto px-6 py-3">
        <div className="flex justify-between items-center">
          <Link href="/" className="text-xl font-bold text-gray-800">
            Vistagram
          </Link>
          <div className="flex items-center">
            {loading && <div className="animate-pulse h-8 w-20 bg-gray-300 rounded-md"></div>}
            
            {!loading && !session && (
              <button
                onClick={() => signIn()}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Login
              </button>
            )}

            {session?.user && (
              <div className="flex items-center space-x-4">
                <span className="text-gray-700">Welcome, {session.user.name ?? session.user.email}</span>
                <button
                  onClick={() => signOut()}
                  className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                >
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>
      </nav>
    </header>
  );
};

export default Header;