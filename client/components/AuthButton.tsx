"use client";

import { useSession, signIn, signOut } from "next-auth/react";
import Link from "next/link";

export default function AuthButton() {
  const { data: session, status } = useSession();
  
  if (status === "loading") {
    return <div className="h-8 w-20 bg-gray-200 animate-pulse rounded"></div>;
  }
  
  if (status === "authenticated") {
    return (
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-700">
          {session.user?.name || session.user?.email}
        </span>
        <button
          onClick={() => signOut()}
          className="text-sm text-gray-600 hover:text-gray-900"
        >
          Sign out
        </button>
      </div>
    );
  }
  
  return (
    <button
      onClick={() => signIn()}
      className="text-sm text-indigo-600 hover:text-indigo-800"
    >
      Sign in
    </button>
  );
}