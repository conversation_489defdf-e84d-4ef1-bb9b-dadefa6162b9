import {
  Controller,
  Get,
  Post,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  Body,
  UseInterceptors,
  UploadedFile,
  UseGuards,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { PostsService } from './posts.service';
import { AuthGuard } from '@nestjs/passport';

@Controller('posts')
export class PostsController {
  constructor(private readonly postsService: PostsService) { }

  @Get()
  findAll() {
    return this.postsService.findAll();
  }

  @Post()
  @UseGuards(AuthGuard('jwt'))
  @UseInterceptors(FileInterceptor('file'))
  create(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { caption: string },
    @Request() req: any
  ) {
    return this.postsService.createPost(file, body.caption, req.user.id);
  }

  @Post(':id/like')
  likePost(@Param('id', ParseIntPipe) id: string) {
    return this.postsService.likePost(id);
  }

  @Delete(':id/like')
  unlikePost(@Param('id', ParseIntPipe) id: string) {
    return this.postsService.unlikePost(id);
  }

  @Post(':id/share')
  @HttpCode(HttpStatus.OK)
  sharePost(@Param('id', ParseIntPipe) id: string) {
    return this.postsService.sharePost(id);
  }
}
