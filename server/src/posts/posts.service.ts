import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { BlobServiceClient, BlockBlobClient } from '@azure/storage-blob';
import { v4 as uuidv4 } from 'uuid';
import { Multer } from 'multer';
import { UpdatePostDto } from './dto/update-post.dto';

@Injectable()
export class PostsService {
  private readonly blobServiceClient = BlobServiceClient.fromConnectionString(
    process.env.AZURE_STORAGE_CONNECTION_STRING || (() => {
      throw new Error('AZURE_STORAGE_CONNECTION_STRING is not configured');
    })(),
  );
  private readonly containerName = process.env.AZURE_STORAGE_CONTAINER_NAME;
  private readonly accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;

  constructor(private prisma: PrismaService) { }

  async createPost(file: Express.Multer.File, caption: string, userId: number) {
    if (!file) {
      throw new InternalServerErrorException('File is required');
    }

    if (!this.containerName) {
      throw new InternalServerErrorException('Azure storage container name is not configured.');
    }

    const containerClient = this.blobServiceClient.getContainerClient(this.containerName);
    const blobName = `${uuidv4()}-${file.originalname}`;
    const blockBlobClient: BlockBlobClient = containerClient.getBlockBlobClient(blobName);

    try {
      await blockBlobClient.uploadData(file.buffer, {
        blobHTTPHeaders: { blobContentType: file.mimetype },
      });

      const imageUrl = `https://${this.accountName}.blob.core.windows.net/${this.containerName}/${blobName}`;

      return this.prisma.post.create({
        data: {
          caption,
          imageUrl,
          authorId: userId, // Use the authenticated user's ID
        },
      });
    } catch (error) {
      console.error('Error uploading to Azure Blob Storage or saving to DB:', error);
      throw new InternalServerErrorException('Failed to create post.');
    }
  }

  findAll() {
    return this.prisma.post.findMany({
      include: {
        author: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    const post = await this.prisma.post.findUnique({ where: { id: Number(id) } });
    if (!post) {
      throw new NotFoundException(`Post with ID "${id}" not found.`);
    }
    return post;
  }

  async likePost(postId: string) {
    const id = parseInt(postId);

    // Check if post exists
    const post = await this.prisma.post.findUnique({
      where: { id }
    });

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Increment likes count
    return this.prisma.post.update({
      where: { id },
      data: {
        likes: {
          increment: 1
        }
      }
    });
  }

  async unlikePost(postId: string) {
    const id = parseInt(postId);

    const post = await this.prisma.post.findUnique({
      where: { id }
    });

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Decrement likes count (but don't go below 0)
    return this.prisma.post.update({
      where: { id },
      data: {
        likes: {
          decrement: post.likes > 0 ? 1 : 0
        }
      }
    });
  }

  async sharePost(postId: string) {
    const id = parseInt(postId);

    const post = await this.prisma.post.findUnique({
      where: { id }
    });

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Increment shares count
    return this.prisma.post.update({
      where: { id },
      data: {
        shares: {
          increment: 1
        }
      }
    });
  }

  async update(id: string, updatePostDto: UpdatePostDto) {
    return this.prisma.post.update({ where: { id: Number(id) }, data: updatePostDto });
  }
}
