// src/auth/auth.controller.ts

import { Controller, Post, Body } from '@nestjs/common';
import { AuthService } from './auth.service';

@Controller('auth') // All routes in this controller will be prefixed with /auth
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register') // Handles POST requests to /auth/register
  async register(@Body() body: { email: string; password: string; name?: string }) {
    return this.authService.register(body.email, body.password, body.name);
  }

  @Post('login') // Handles POST requests to /auth/login
  async login(@Body() body: { email: string; password: string }) {
    return this.authService.login(body.email, body.password);
  }
}
