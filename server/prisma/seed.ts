// server/prisma/seed.ts
import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

const getPosts = () => [
  {
    caption: 'Golden hour at the Grand Canyon. Nature never ceases to amaze. #grandcanyon #sunset #travelphotography',
    imageUrl: 'https://vistagram.blob.core.windows.net/posts/grand_canyon.png',
    likes: 120,
    shares: 15,
  },
  {
    caption: 'Exploring the ancient streets of Rome. Every corner tells a story. #rome #italy #history',
    imageUrl: 'https://vistagram.blob.core.windows.net/posts/rome.png',
    likes: 250,
    shares: 30,
  },
  {
    caption: 'Lost in the vibrant markets of Marrakech. A feast for the senses! #marrakech #morocco #travel',
    imageUrl: 'https://vistagram.blob.core.windows.net/posts/marrakech.png',
    likes: 10,
    shares: 22,
  },
];

async function main() {
  console.log('Start seeding...');

  // 1. Clean up previous data
  console.log('Deleting old posts...');
  await prisma.post.deleteMany();
  console.log('Deleting old users...');
  await prisma.user.deleteMany();
  console.log('Old data cleaned up.');

  // 2. Create a dummy user
  console.log('Creating a seed user...');
  const hashedPassword = await bcrypt.hash('password123', 10);
  const user = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Vistagram Bot',
      password: hashedPassword,
    },
  });
  console.log(`Created user '${user.name}' with id: ${user.id}`);

  // 3. Create posts and connect them to the new user
  console.log('Creating new posts...');
  const posts = getPosts();
  for (const post of posts) {
    await prisma.post.create({
      data: {
        ...post, // Use spread operator for caption, imageUrl, etc.
        authorId: user.id, // Assign the user we just created
      },
    });
  }
  console.log(`Seeded ${posts.length} posts for user ${user.id}.`);
  console.log('Seeding finished.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
