
datasource db {
  provider = "postgresql" // We are using PostgreSQL
  url      = env("DATABASE_URL") // This loads the URL from your .env file
}

generator client {
  provider = "prisma-client-js"
}

// Vistagram User Model
model User {
  id        Int         @id @default(autoincrement())
  email     String      @unique
  name      String?
  password  String      // We will store a HASHED password, never plain text
  posts     Post[]      // Relation to Post model
  likes     PostLike[]  // Posts this user has liked
  shares    PostShare[] // Posts this user has shared
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
}

// Vistagram Post Model
model Post {
  id          Int         @id @default(autoincrement())
  caption     String
  imageUrl    String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Author relationship
  author      User        @relation(fields: [authorId], references: [id])
  authorId    Int
  
  // Many-to-many relationships for likes and shares
  likes       PostLike[]
  shares      PostShare[]
}

// Join table for User-Post likes
model PostLike {
  id      Int      @id @default(autoincrement())
  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId  Int
  post    Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  postId  Int
  createdAt DateTime @default(now())
  
  @@unique([userId, postId]) // Prevent duplicate likes from same user
}

// Join table for User-Post shares
model PostShare {
  id      Int      @id @default(autoincrement())
  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId  Int
  post    Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  postId  Int
  createdAt DateTime @default(now())
  
  @@unique([userId, postId]) // Prevent duplicate shares from same user
}
